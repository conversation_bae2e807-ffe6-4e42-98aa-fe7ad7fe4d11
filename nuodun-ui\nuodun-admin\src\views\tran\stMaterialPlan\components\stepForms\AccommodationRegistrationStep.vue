<template>
  <div class="accommodation-registration-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>住宿申请&注册详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>是否申请住宿：</label>
            <span>{{ stepData.accommodationApplied ? '是' : '否' }}</span>
          </div>
          <div v-if="stepData.accommodationApplied && stepData.accommodationTime" class="info-item">
            <label>住宿申请时间：</label>
            <span>{{ stepData.accommodationTime }}</span>
          </div>
          <div class="info-item">
            <label>注册状态：</label>
            <el-tag :type="stepData.registrationCompleted ? 'success' : 'warning'">
              {{ stepData.registrationCompleted ? '已注册' : '未注册' }}
            </el-tag>
          </div>
          <div v-if="stepData.registrationCompleted && stepData.registrationTime" class="info-item">
            <label>注册时间：</label>
            <span>{{ stepData.registrationTime }}</span>
          </div>
          <div v-if="stepData.enrollmentConfirmed !== null" class="info-item">
            <label>最终入学确认：</label>
            <el-tag :type="getEnrollmentTagType(stepData.enrollmentConfirmed)">
              {{ getEnrollmentText(stepData.enrollmentConfirmed) }}
            </el-tag>
          </div>
          <div v-if="stepData.confirmTime" class="info-item">
            <label>确认时间：</label>
            <span>{{ stepData.confirmTime }}</span>
          </div>
          <div v-if="stepData.remark" class="info-item">
            <label>备注说明：</label>
            <span>{{ stepData.remark }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="是否申请住宿" prop="accommodationApplied">
          <el-radio-group v-model="stepData.accommodationApplied">
            <el-radio :label="true">申请住宿</el-radio>
            <el-radio :label="false">不申请住宿</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="stepData.accommodationApplied" label="住宿申请时间" prop="accommodationTime">
          <el-date-picker
            v-model="stepData.accommodationTime"
            type="datetime"
            placeholder="请选择住宿申请时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="注册状态" prop="registrationCompleted">
          <el-radio-group v-model="stepData.registrationCompleted">
            <el-radio :label="true">已完成注册</el-radio>
            <el-radio :label="false">未完成注册</el-radio>
          </el-radio-group>
          <div class="form-tip">注册为所有学生的必办流程</div>
        </el-form-item>

        <el-form-item v-if="stepData.registrationCompleted" label="注册时间" prop="registrationTime">
          <el-date-picker
            v-model="stepData.registrationTime"
            type="datetime"
            placeholder="请选择注册完成时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="最终入学确认" prop="enrollmentConfirmed">
          <el-radio-group v-model="stepData.enrollmentConfirmed">
            <el-radio :label="true">确认入学</el-radio>
            <el-radio :label="false">放弃入学</el-radio>
            <el-radio :label="null">待确认</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="stepData.enrollmentConfirmed !== null" label="确认时间" prop="confirmTime">
          <el-date-picker
            v-model="stepData.confirmTime"
            type="datetime"
            placeholder="请选择确认时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="住宿申请和注册相关的其他信息"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据 - 按照新的JSON字段配置
const stepData = reactive({
  accommodationApplied: false,     // 是否申请住宿
  accommodationTime: '',           // 住宿申请时间
  registrationCompleted: false,    // 注册状态
  registrationTime: '',            // 注册时间
  enrollmentConfirmed: null,       // 最终入学确认（true/false/null）
  confirmTime: '',                 // 确认时间
  remark: ''                       // 备注说明
})

// 验证规则
const rules = {
  accommodationApplied: [
    { required: true, message: '请选择是否申请住宿', trigger: 'change' }
  ],
  accommodationTime: [
    { 
      validator: (rule, value, callback) => {
        if (stepData.accommodationApplied && !value) {
          callback(new Error('请选择住宿申请时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  registrationCompleted: [
    { required: true, message: '请选择注册状态', trigger: 'change' }
  ],
  registrationTime: [
    { 
      validator: (rule, value, callback) => {
        if (stepData.registrationCompleted && !value) {
          callback(new Error('请选择注册时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  enrollmentConfirmed: [
    { required: true, message: '请选择最终入学确认状态', trigger: 'change' }
  ],
  confirmTime: [
    { 
      validator: (rule, value, callback) => {
        if (stepData.enrollmentConfirmed !== null && !value) {
          callback(new Error('请选择确认时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 获取入学确认标签类型
const getEnrollmentTagType = (status) => {
  if (status === true) return 'success'
  if (status === false) return 'danger'
  return 'warning'
}

// 获取入学确认文本
const getEnrollmentText = (status) => {
  if (status === true) return '确认入学'
  if (status === false) return '放弃入学'
  return '待确认'
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    accommodationApplied: false,
    accommodationTime: '',
    registrationCompleted: false,
    registrationTime: '',
    enrollmentConfirmed: null,
    confirmTime: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    accommodationApplied: false,
    accommodationTime: '',
    registrationCompleted: false,
    registrationTime: '',
    enrollmentConfirmed: null,
    confirmTime: '',
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.accommodation-registration-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 140px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }

    .form-tip {
      color: #909399;
      font-size: 12px;
      margin-top: 4px;
    }
  }
}
</style> 
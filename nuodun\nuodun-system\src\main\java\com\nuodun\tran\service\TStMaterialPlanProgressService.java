package com.nuodun.tran.service;

import com.nuodun.tran.domain.TStMaterialPlanProgress;
import com.nuodun.tran.domain.TStMaterialPlanProgressLog;
import com.nuodun.tran.domain.dto.UpdateProgressDto;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 专业申请流程进度Service接口 - 重构版
 * 支持新的12步骤流程和数据库驱动的步骤配置
 *
 * <AUTHOR>
 * @description 针对表【t_st_material_plan_progress(专业申请流程进度表)】的数据库操作Service
 * @createDate 2025-07-30
 */
public interface TStMaterialPlanProgressService extends IService<TStMaterialPlanProgress> {

    // =========================
    // 基础CRUD操作
    // =========================

    /**
     * 根据专业计划ID获取进度信息
     * @param planId 专业计划ID
     * @return 进度信息
     */
    TStMaterialPlanProgress getByPlanId(Long planId);

    /**
     * 根据学生ID获取所有进度信息
     * @param stId 学生ID
     * @return 进度信息列表
     */
    List<TStMaterialPlanProgress> getByStId(Long stId);

    /**
     * 获取进度操作日志
     * @param progressId 进度ID
     * @return 操作日志列表
     */
    List<TStMaterialPlanProgressLog> getLogs(Long progressId);

    /**
     * 初始化专业申请进度
     * 创建新的进度记录并初始化默认的12步骤配置
     * @param planId 专业计划ID
     * @param stId 学生ID
     * @param contractId 合同ID
     * @param orderId 订单ID
     * @param stDecaType 学生类型
     * @param applySchool 申请学校
     * @param applyMajorZh 申请专业中文名
     * @param applyMajorEn 申请专业英文名
     * @return 创建的进度记录
     */
    TStMaterialPlanProgress initProgress(Long planId, Long stId, Long contractId, String orderId,
                                       String stDecaType, String applySchool, String applyMajorZh, String applyMajorEn);

    /**
     * 统一更新进度信息
     * 支持多种更新类型：stepComplete(完成步骤)、progressStatus(更新整体状态)、stepConfig(更新步骤配置)
     * @param progressId 进度ID
     * @param updateData 更新数据DTO
     * @param operatorId 操作人ID
     * @return 更新结果
     */
    boolean updateProgress(Long progressId, UpdateProgressDto updateData, Long operatorId);

    // =========================
    // 步骤配置相关方法
    // =========================

    /**
     * 初始化步骤配置
     * 为指定进度记录创建默认的12步骤配置
     * @param progressId 进度ID
     * @return 初始化结果
     */
    boolean initializeStepsConfig(Long progressId);

    /**
     * 获取步骤配置
     * @param progressId 进度ID
     * @return 步骤配置JSON
     */
    String getStepsConfig(Long progressId);

    /**
     * 更新步骤配置
     * @param progressId 进度ID
     * @param stepsConfig 步骤配置JSON
     * @param operatorId 操作人ID
     * @return 更新结果
     */
    boolean updateStepsConfig(Long progressId, String stepsConfig, Long operatorId);

    // =========================
    // 步骤数据相关方法
    // =========================

    /**
     * 获取步骤数据
     * @param progressId 进度ID
     * @return 步骤数据JSON
     */
    String getStepsData(Long progressId);

    /**
     * 更新步骤数据
     * @param progressId 进度ID
     * @param stepsData 步骤数据JSON
     * @param operatorId 操作人ID
     * @return 更新结果
     */
    boolean updateStepsData(Long progressId, String stepsData, Long operatorId);

    /**
     * 获取指定步骤的数据
     * @param progressId 进度ID
     * @param stepCode 步骤编码
     * @return 步骤数据Map
     */
    Map<String, Object> getStepData(Long progressId, String stepCode);

    /**
     * 更新指定步骤的数据
     * @param progressId 进度ID
     * @param stepCode 步骤编码
     * @param stepData 步骤数据
     * @param operatorId 操作人ID
     * @return 更新结果
     */
    boolean updateStepData(Long progressId, String stepCode, Map<String, Object> stepData, Long operatorId);

    // =========================
    // 步骤流程控制方法
    // =========================

    /**
     * 完成步骤
     * 标记指定步骤为已完成，并根据跳转选项推进到下一步
     * @param progressId 进度ID
     * @param stepCode 步骤编码
     * @param stepData 步骤数据
     * @param jumpOption 跳转选项
     * @param operatorId 操作人ID
     * @return 完成结果
     */
    boolean completeStep(Long progressId, String stepCode, Map<String, Object> stepData, 
                        UpdateProgressDto.JumpOption jumpOption, Long operatorId);

    /**
     * 跳过步骤
     * 标记指定步骤为已跳过，并推进到下一步
     * @param progressId 进度ID
     * @param stepCode 步骤编码
     * @param reason 跳过原因
     * @param operatorId 操作人ID
     * @return 跳过结果
     */
    boolean skipStep(Long progressId, String stepCode, String reason, Long operatorId);

    /**
     * 重置到指定步骤
     * 管理员功能：用于纠正错误的进度状态
     * @param progressId 进度ID
     * @param stepCode 目标步骤编码
     * @param operatorId 操作人ID
     * @return 重置结果
     */
    boolean resetToStep(Long progressId, String stepCode, Long operatorId);

    /**
     * 获取下一步骤
     * 根据当前步骤和跳转选项计算下一步骤
     * @param progressId 进度ID
     * @param currentStepCode 当前步骤编码
     * @param jumpOption 跳转选项
     * @return 下一步骤编码
     */
    String getNextStep(Long progressId, String currentStepCode, UpdateProgressDto.JumpOption jumpOption);

    /**
     * 检查步骤是否可以完成
     * @param progressId 进度ID
     * @param stepCode 步骤编码
     * @return 是否可以完成
     */
    boolean canCompleteStep(Long progressId, String stepCode);

    /**
     * 检查步骤是否可以跳过  
     * @param progressId 进度ID
     * @param stepCode 步骤编码
     * @return 是否可以跳过
     */
    boolean canSkipStep(Long progressId, String stepCode);

    // =========================
    // 进度状态管理方法
    // =========================

    /**
     * 更新整体进度状态
     * @param progressId 进度ID
     * @param progressStatus 进度状态
     * @param operatorId 操作人ID
     * @return 更新结果
     */
    boolean updateProgressStatus(Long progressId, String progressStatus, Long operatorId);

    /**
     * 完成整个申请流程
     * @param progressId 进度ID
     * @param operatorId 操作人ID
     * @return 完成结果
     */
    boolean completeProgress(Long progressId, Long operatorId);

    /**
     * 取消申请流程
     * @param progressId 进度ID
     * @param reason 取消原因
     * @param operatorId 操作人ID
     * @return 取消结果
     */
    boolean cancelProgress(Long progressId, String reason, Long operatorId);

    // =========================
    // 统计和报表方法
    // =========================

    /**
     * 获取步骤统计信息
     * @param progressId 进度ID
     * @return 统计信息
     */
    Map<String, Object> getStepStatistics(Long progressId);

    /**
     * 获取进度百分比
     * @param progressId 进度ID
     * @return 进度百分比(0-100)
     */
    int getProgressPercentage(Long progressId);

    /**
     * 获取预计完成时间
     * @param progressId 进度ID
     * @return 预计完成时间
     */
    String getEstimatedCompletionTime(Long progressId);

    // =========================
    // JSON工具方法
    // =========================

    /**
     * 解析步骤配置JSON
     * @param stepsConfig 步骤配置JSON字符串
     * @return 解析后的Map对象
     */
    Map<String, Object> parseStepsConfig(String stepsConfig);

    /**
     * 解析步骤数据JSON
     * @param stepsData 步骤数据JSON字符串
     * @return 解析后的Map对象
     */
    Map<String, Object> parseStepsData(String stepsData);

    /**
     * 转换为步骤配置JSON
     * @param stepsConfigMap 步骤配置Map
     * @return JSON字符串
     */
    String toStepsConfigJson(Map<String, Object> stepsConfigMap);

    /**
     * 转换为步骤数据JSON
     * @param stepsDataMap 步骤数据Map
     * @return JSON字符串
     */
    String toStepsDataJson(Map<String, Object> stepsDataMap);
}

<template>
  <div>
    <el-dialog v-model="dialogVisible" :width="dialogWidth" append-to-body :destroy-on-close="true"
      :close-on-click-modal="false" class="custom-dialog" :fullscreen="isFullscreen">
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">{{ dialogTitle }}</span>
        </div>
      </template>
      <div class="progress-container" :class="{ 'fullscreen-container': isFullscreen }"
        :style="{ maxWidth: isFullscreen ? '100%' : '960px' }">
        <!-- 优化的学生信息头部 -->
        <div class="student-info-header">
          <el-card class="info-card">
            <div class="student-header-content">
              <!-- 左侧学生基本信息 -->
              <div class="student-basic">
                <div class="student-name-section">
                  <h3>{{ studentInfo.stName }}</h3>
                  <div class="school-major">
                    <el-tag type="primary" size="default">{{ progressData.applySchool }}</el-tag>
                    <el-tag type="success" size="default">{{ progressData.applyMajorZh }}</el-tag>
                  </div>
                </div>
                <div class="progress-status">
                  <el-tag :type="progressData.progressStatus === 'COMPLETED' ? 'success' : 'primary'" size="large">
                    {{ progressData.progressStatus === 'COMPLETED' ? '已完成' : '进行中' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
          <!-- Tab切换区域 -->
          <div class="content-tabs">
            <el-tabs v-model="activeTab" type="card">
              <!-- 升学流程操作Tab - 只在升学模块显示 -->
              <el-tab-pane v-if="showProgressSteps" label="升学流程" name="operation">
                <!-- 流程已完成提示 - 显示在进度步骤上方 -->
                <div v-if="progressData.progressStatus === 'COMPLETED'" class="process-completed-notice">
                  <el-alert title="该申请流程已经结束，当前步骤位置保持不变" description="流程已完成，表单都不可再次填写" type="success" :closable="false"
                    show-icon center />
                </div>

                <!-- 升学流程进度步骤 -->
                <div class="progress-steps">
                  <div class="steps-container">
                    <el-button class="step-nav-btn prev-btn" :disabled="stepScrollIndex === 0"
                      @click="scrollSteps('prev')" size="small" circle>
                      <el-icon>
                        <ArrowLeft />
                      </el-icon>
                    </el-button>

                    <div class="steps-wrapper" ref="stepsWrapper">
                      <div class="steps-content"
                        :style="{ transform: `translateX(-${stepScrollIndex * stepItemWidth}px)` }">
                        <div v-for="(step, index) in progressSteps" :key="step.code" class="step-item" :class="{
                          'step-active': step.code === selectedStepCode,
                          'step-clickable': canStepClick(step),
                          'step-current': step.code === progressData.currentStep,
                          'step-completed': getStepStatus(step) === 'finish',
                          'step-skipped': getStepStatus(step) === 'error'
                        }" @click="handleStepClick(step)">
                          <div class="step-icon" :class="getStepIconClass(step)">
                            <span class="step-number">{{ index + 1 }}</span>
                          </div>
                          <div class="step-content">
                            <div class="step-title">{{ step.name }}</div>
                            <div class="step-description">{{ getStepDescription(step) }}</div>
                          </div>
                          <div v-if="index < progressSteps.length - 1" class="step-connector"
                            :class="getConnectorClass(step, progressSteps[index + 1])"></div>
                        </div>
                      </div>
                    </div>

                    <el-button class="step-nav-btn next-btn" :disabled="stepScrollIndex >= maxScrollIndex"
                      @click="scrollSteps('next')" size="small" circle>
                      <el-icon>
                        <ArrowRight />
                      </el-icon>
                    </el-button>
                  </div>
                </div>

                <!-- 步骤操作表单 -->
                <el-card class="operation-card">
                  <template #header>
                    <div class="card-header">
                      <span>{{ selectedStep.name }}</span>
                      <el-tag :type="getStepTagType(selectedStep)">
                        {{ getStepStatusText(selectedStep) }}
                      </el-tag>
                    </div>
                  </template>

                  <StepOperationForm :step="selectedStep" :progress-data="progressData"
                    :readonly="!isCurrentStepEditable" @submit="handleStepSubmit" @endProcess="handleEndProcess"
                    @clearForm="handleClearForm" />
                </el-card>
              </el-tab-pane>

              <!-- 邮件记录Tab -->
              <el-tab-pane label="邮件记录" name="email">
                <div class="email-content">
                  <EmailRecordList :progress-id="progressData.id" :plan-id="progressData.planId"
                    :st-id="progressData.stId" :show-pagination="false" />
                </div>
              </el-tab-pane>

              <!-- 操作日志Tab -->
              <el-tab-pane label="操作日志" name="logs">
                <div class="logs-content">
                  <OperationLogList :progress-id="progressData.id" :show-pagination="false" />
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button class="custom-dialog-btn" @click="toggleFullscreen">
            {{ isFullscreen ? '退出全屏' : '全屏显示' }}
          </el-button>
          <el-button class="custom-dialog-btn" @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, watch } from 'vue'
import { getByPlanId, updateProgress } from '@/api/tran/materialPlanProgress'
import StepOperationForm from './components/StepOperationForm.vue'
import EmailRecordList from './components/EmailRecordList.vue'
import OperationLogList from './components/OperationLogList.vue'
import { PROGRESS_STEPS, getStepInfo, getProgressPercentage, createDefaultStepsConfig } from '@/utils/progressStepConstants'

const { proxy } = getCurrentInstance()

// 获取步骤图标映射 - 更新为12步骤
function getStepIconByCode(stepCode) {
  const iconMap = {
    'WRITING_PROCESS': 'el-icon-edit-outline',
    'WRITING_FINALIZATION': 'el-icon-document',
    'APPLICATION_FILLING': 'el-icon-edit',
    'INTERVIEW_INVITATION': 'el-icon-message',
    'INTERVIEW_TRAINING': 'el-icon-video-camera',
    'ATTEND_INTERVIEW': 'el-icon-user',
    'ADMISSION_NOTICE': 'el-icon-bell',
    'ACCEPT_OFFER_DEPOSIT': 'el-icon-coin',
    'VISA_PREPARATION': 'el-icon-folder',
    'CONDITIONAL_MATERIALS': 'el-icon-document-add',
    'VISA_FORMAL_OFFER': 'el-icon-trophy',
    'ACCOMMODATION_REGISTRATION': 'el-icon-school'
  }
  return iconMap[stepCode] || 'el-icon-circle-check'
}

// 进度步骤配置 - 使用常量定义
const progressSteps = PROGRESS_STEPS.map(step => ({
  ...step,
  icon: getStepIconByCode(step.code)
}))

// 弹窗控制
const dialogVisible = ref(false)
const dialogWidth = computed(() => {
  return '1000px'
})
const isFullscreen = ref(false)

// 数据
const studentInfo = ref({})
const progressData = ref({})
const stepsConfig = ref({}) // 从数据库读取的步骤配置
const activeTab = ref('operation')

// 模式控制
const viewMode = ref('view') // 'view' 查看模式, 'edit' 修改模式

// 控制是否显示进度步骤（只在升学模块显示）
const showProgressSteps = ref(true)

// 步骤滑动相关
const stepScrollIndex = ref(0)
const stepItemWidth = 180 // 每个步骤项的宽度
const stepsWrapper = ref(null)

// 动态计算可见步骤数量，确保在1000px宽度下能够显示合适的步骤数
const visibleStepsCount = computed(() => {
  if (isFullscreen.value) {
    return Math.min(8, progressSteps.length) // 全屏时显示更多步骤
  }
  return Math.min(5, progressSteps.length) // 普通模式下显示5个步骤
})

// 选中的步骤
const selectedStepCode = ref('')

// 动态标题
const dialogTitle = computed(() => {
  if (showProgressSteps.value && selectedStepCode.value) {
    const step = progressSteps.find(s => s.code === selectedStepCode.value)
    return step ? `申请跟踪 - ${step.name}` : '申请跟踪'
  }
  return '申请跟踪'
})

// 当前步骤
const currentStep = computed(() => {
  const currentStepCode = progressData.value.currentStep || 'WRITING_PROCESS'
  return progressSteps.find(step => step.code === currentStepCode) || progressSteps[0]
})

// 选中的步骤对象
const selectedStep = computed(() => {
  return progressSteps.find(step => step.code === selectedStepCode.value) || currentStep.value
})

// 最大滚动索引
const maxScrollIndex = computed(() => {
  // 确保最后一个步骤能够完全显示，增加1的缓冲
  return Math.max(0, progressSteps.length - visibleStepsCount.value + 1)
})

// 判断步骤是否可以点击
const canStepClick = (step) => {
  const stepStatus = getStepStatus(step)
  // 已完成、已跳过或当前步骤可以点击
  return stepStatus === 'finish' || stepStatus === 'error' || step.code === progressData.value.currentStep
}

// 判断当前选中的步骤是否可编辑
const isCurrentStepEditable = computed(() => {
  // 如果整个流程已完成，所有步骤都不可编辑
  if (progressData.value.progressStatus === 'COMPLETED') {
    return false
  }
  // 只有当前步骤可以编辑
  return selectedStepCode.value === progressData.value.currentStep
})

// 获取步骤状态 - 从steps_config或stepsData中读取
const getStepStatus = (step) => {
  // 优先从steps_config中读取状态
  if (stepsConfig.value.steps && stepsConfig.value.steps[step.code]) {
    const stepStatus = stepsConfig.value.steps[step.code].status
    if (stepStatus === 'COMPLETED') return 'finish'
    if (stepStatus === 'SKIPPED') return 'error'
    if (step.code === progressData.value.currentStep) return 'process'
    return 'wait'
  }

  // 回退到旧的字段映射（兼容性）
  const stepStatus = getStepFieldValue(step.code)
  if (stepStatus === 'COMPLETED') return 'finish'
  if (stepStatus === 'SKIPPED') return 'error'
  if (step.code === progressData.value.currentStep) return 'process'
  return 'wait'
}

// 获取步骤描述
const getStepDescription = (step) => {
  const stepStatus = getStepStatus(step)

  // 从stepsData中获取时间信息
  if (progressData.value.stepsData) {
    try {
      const stepsData = typeof progressData.value.stepsData === 'string'
        ? JSON.parse(progressData.value.stepsData)
        : progressData.value.stepsData

      if (stepsData[step.code] && stepsData[step.code].operationTime) {
        const timeStr = stepsData[step.code].operationTime
        if (timeStr) {
          try {
            const date = new Date(timeStr)
            if (!isNaN(date.getTime())) {
              const timePrefix = getStepTimePrefix(step.code)
              return `${timePrefix}：${date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
              })}`
            }
          } catch (error) {
            console.warn('时间格式化失败:', error)
          }
        }
      }
    } catch (error) {
      console.warn('解析stepsData失败:', error)
    }
  }

  // 回退到旧的字段映射（兼容性）
  const stepTime = getStepTimeValue(step.code)
  if (stepStatus === 'finish' && stepTime) {
    try {
      const date = new Date(stepTime)
      if (!isNaN(date.getTime())) {
        const timePrefix = getStepTimePrefix(step.code)
        return `${timePrefix}：${date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })}`
      }
    } catch (error) {
      console.warn('时间格式化失败:', error)
    }
  }

  if (stepStatus === 'error') return '已跳过'
  if (step.code === progressData.value.currentStep) return '进行中'
  return step.description || ''
}

// 根据步骤代码获取时间前缀 - 更新为12步骤
const getStepTimePrefix = (stepCode) => {
  switch (stepCode) {
    case 'WRITING_PROCESS':
      return '文书约谈时间'
    case 'WRITING_FINALIZATION':
      return '文书定稿时间'
    case 'APPLICATION_FILLING':
      return '申请填写时间'
    case 'INTERVIEW_INVITATION':
      return '面试邀请时间'
    case 'INTERVIEW_TRAINING':
      return '面试培训时间'
    case 'ATTEND_INTERVIEW':
      return '面试时间'
    case 'ADMISSION_NOTICE':
      return '录取通知时间'
    case 'ACCEPT_OFFER_DEPOSIT':
      return '留位费缴纳时间'
    case 'VISA_PREPARATION':
      return '签证准备时间'
    case 'CONDITIONAL_MATERIALS':
      return '条件材料递交时间'
    case 'VISA_FORMAL_OFFER':
      return '正式录取时间'
    case 'ACCOMMODATION_REGISTRATION':
      return '注册时间'
    default:
      return '完成时间'
  }
}

// 获取步骤图标类名
const getStepIconClass = (step) => {
  const status = getStepStatus(step)
  return {
    'step-completed': status === 'finish',
    'step-current': status === 'process',
    'step-skipped': status === 'error',
    'step-waiting': status === 'wait'
  }
}

// 获取步骤图标
const getStepIcon = (step) => {
  const status = getStepStatus(step)
  if (status === 'finish') return 'el-icon-check'
  if (status === 'error') return 'el-icon-close'
  return step.icon
}

// 获取步骤标签类型
const getStepTagType = (step) => {
  const status = getStepStatus(step)
  if (status === 'finish') return 'success'
  if (status === 'process') return 'primary'
  if (status === 'error') return 'warning'
  return 'info'
}

// 获取步骤状态文本
const getStepStatusText = (step) => {
  const status = getStepStatus(step)
  if (status === 'finish') return '已完成'
  if (status === 'process') return '进行中'
  if (status === 'error') return '已跳过'
  return '待处理'
}

// 兼容性方法：获取步骤字段值 - 保留用于向后兼容
const getStepFieldValue = (stepCode) => {
  const fieldMap = {
    // 新的12步骤映射
    'WRITING_PROCESS': 'stepWritingProcessStatus',
    'WRITING_FINALIZATION': 'stepWritingFinalizationStatus',
    'APPLICATION_FILLING': 'stepApplicationFillingStatus',
    'INTERVIEW_INVITATION': 'stepInterviewInvitationStatus',
    'INTERVIEW_TRAINING': 'stepInterviewTrainingStatus',
    'ATTEND_INTERVIEW': 'stepAttendInterviewStatus',
    'ADMISSION_NOTICE': 'stepAdmissionNoticeStatus',
    'ACCEPT_OFFER_DEPOSIT': 'stepAcceptOfferDepositStatus',
    'VISA_PREPARATION': 'stepVisaPreparationStatus',
    'CONDITIONAL_MATERIALS': 'stepConditionalMaterialsStatus',
    'VISA_FORMAL_OFFER': 'stepVisaFormalOfferStatus',
    'ACCOMMODATION_REGISTRATION': 'stepAccommodationRegistrationStatus'
  }
  return progressData.value[fieldMap[stepCode]]
}

// 兼容性方法：获取步骤时间值 - 保留用于向后兼容
const getStepTimeValue = (stepCode) => {
  const timeFieldMap = {
    // 新的12步骤时间字段映射
    'WRITING_PROCESS': 'stepWritingProcessOperationTime',
    'WRITING_FINALIZATION': 'stepWritingFinalizationOperationTime',
    'APPLICATION_FILLING': 'stepApplicationFillingOperationTime',
    'INTERVIEW_INVITATION': 'stepInterviewInvitationOperationTime',
    'INTERVIEW_TRAINING': 'stepInterviewTrainingOperationTime',
    'ATTEND_INTERVIEW': 'stepAttendInterviewOperationTime',
    'ADMISSION_NOTICE': 'stepAdmissionNoticeOperationTime',
    'ACCEPT_OFFER_DEPOSIT': 'stepAcceptOfferDepositOperationTime',
    'VISA_PREPARATION': 'stepVisaPreparationOperationTime',
    'CONDITIONAL_MATERIALS': 'stepConditionalMaterialsOperationTime',
    'VISA_FORMAL_OFFER': 'stepVisaFormalOfferOperationTime',
    'ACCOMMODATION_REGISTRATION': 'stepAccommodationRegistrationOperationTime'
  }
  return progressData.value[timeFieldMap[stepCode]]
}

// 获取连接线样式
const getConnectorClass = (currentStep, nextStep) => {
  const currentStatus = getStepStatus(currentStep)
  return {
    'connector-completed': currentStatus === 'finish',
    'connector-skipped': currentStatus === 'error',
    'connector-default': currentStatus !== 'finish' && currentStatus !== 'error'
  }
}

// 处理步骤点击
const handleStepClick = (step) => {
  if (canStepClick(step)) {
    selectedStepCode.value = step.code
  }
}

// 滚动步骤
const scrollSteps = (direction) => {
  if (direction === 'prev' && stepScrollIndex.value > 0) {
    stepScrollIndex.value--
  } else if (direction === 'next' && stepScrollIndex.value < maxScrollIndex.value) {
    stepScrollIndex.value++
  }
}

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

// 切换模式
const switchMode = (mode) => {
  viewMode.value = mode
}

// 处理清空表单
const handleClearForm = () => {
}

// 初始化步骤配置
const initializeStepsConfig = () => {
  if (!progressData.value.stepsConfig) {
    stepsConfig.value = createDefaultStepsConfig()
  } else {
    try {
      stepsConfig.value = typeof progressData.value.stepsConfig === 'string'
        ? JSON.parse(progressData.value.stepsConfig)
        : progressData.value.stepsConfig
    } catch (error) {
      console.warn('解析steps_config失败，使用默认配置:', error)
      stepsConfig.value = createDefaultStepsConfig()
    }
  }
}

// 打开弹窗
const open = async (student, materialPlan, moduleType = 'admission') => {
  studentInfo.value = student

  // 根据模块类型决定是否显示进度步骤和默认Tab
  showProgressSteps.value = moduleType === 'admission' // 只有升学模块显示进度步骤
  activeTab.value = showProgressSteps.value ? 'operation' : 'email'

  // 重置状态
  stepScrollIndex.value = 0
  isFullscreen.value = false
  viewMode.value = 'view'

  try {
    // 获取进度数据
    const response = await getByPlanId(materialPlan.id)
    if (response.data) {
      progressData.value = response.data

      // 初始化步骤配置
      initializeStepsConfig()

      // 初始化选中的步骤为当前步骤
      const currentStepCode = response.data.currentStep || 'WRITING_PROCESS'
      selectedStepCode.value = currentStepCode

      // 自动滚动到当前步骤位置
      const currentStepIndex = progressSteps.findIndex(step => step.code === currentStepCode)
      if (currentStepIndex >= visibleStepsCount.value - 1) {
        // 确保当前步骤在可见区域内，并且最后一个步骤能够完全显示
        stepScrollIndex.value = Math.min(
          Math.max(0, currentStepIndex - Math.floor(visibleStepsCount.value / 2)),
          maxScrollIndex.value
        )
      }

      dialogVisible.value = true
    } else {
      // 如果没有进度数据，初始化一个
      progressData.value = {
        planId: materialPlan.id,
        stId: student.stId,
        applySchool: materialPlan.applySchool,
        applyMajorZh: materialPlan.applyMajorZh,
        currentStep: 'WRITING_PROCESS',
        progressStatus: 'IN_PROGRESS'
      }

      // 初始化步骤配置
      initializeStepsConfig()

      selectedStepCode.value = 'WRITING_PROCESS'
      dialogVisible.value = true
    }
  } catch (error) {
    console.error('获取进度数据失败:', error)
    proxy.$modal.msgError('获取进度数据失败')
    // 不打开弹窗
  }
}

// 处理步骤提交
const handleStepSubmit = async (submitData) => {
  try {
    // 构建更新数据
    const updateData = {
      updateType: 'stepComplete',
      stepCode: submitData.stepCode,
      stepData: submitData.stepData,
      jumpOption: submitData.jumpOption,
      stepStatus: 'COMPLETED',
      stepSelectTime: new Date()
    }

    await updateProgress(progressData.value.id, updateData)
    proxy.$modal.msgSuccess('步骤已完成')

    // 重新获取进度数据
    const response = await getByPlanId(progressData.value.planId)
    if (response.data) {
      const oldCurrentStep = progressData.value.currentStep
      progressData.value = response.data

      // 重新初始化步骤配置
      initializeStepsConfig()

      // 如果当前步骤发生了变化，自动跳转到下一步
      if (response.data.currentStep !== oldCurrentStep) {
        selectedStepCode.value = response.data.currentStep
        proxy.$modal.msgSuccess('已自动跳转到下一步')
      }
    }
  } catch (error) {
    console.error('步骤更新失败:', error)
    proxy.$modal.msgError('步骤更新失败')
  }
}

// 处理结束整个流程
const handleEndProcess = async (endData) => {
  try {
    const updateData = {
      updateType: 'progressStatus',
      progressStatus: 'COMPLETED',
      stepRemark: endData.stepRemark || '手动结束流程',
      stepSelectTime: endData.stepSelectTime || ''
    }
    await updateProgress(progressData.value.id, updateData)
    proxy.$modal.msgSuccess('申请流程已结束，当前步骤位置保持不变')

    // 重新获取进度数据以更新界面
    const response = await getByPlanId(progressData.value.planId)
    if (response.data) {
      progressData.value = response.data
      initializeStepsConfig()
    }

    dialogVisible.value = false
  } catch (error) {
    console.error('结束流程失败:', error)
    proxy.$modal.msgError('结束流程失败')
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  studentInfo.value = {}
  progressData.value = {}
  stepsConfig.value = {}
  activeTab.value = 'operation'
  selectedStepCode.value = ''
  stepScrollIndex.value = 0
  showProgressSteps.value = true
}

defineExpose({
  open
})
</script>

<style scoped lang="scss">
.progress-container {
  min-height: 40vh;
  max-height: 70vh;
  padding: 0 10px;
  overflow-x: auto;
  overflow-y: auto;

  &.fullscreen-container {
    max-height: calc(100vh - 180px);
    height: calc(100vh - 180px);
  }

  // 响应式设计
  @media (max-width: 1200px) {
    padding: 0 5px;
    max-height: 60vh;
  }

  @media (max-width: 768px) {
    padding: 0;
    max-height: 50vh;
  }

  .student-info-header {
    margin-bottom: 20px;

    .info-card {
      :deep(.el-card__body) {
        padding: 20px;
      }

      .student-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 20px;

        .student-basic {
          display: flex;
          align-items: center;
          gap: 20px;
          flex: 1;

          .student-name-section {
            h3 {
              margin: 0 0 8px 0;
              color: #303133;
              font-size: 20px;
              font-weight: 600;
            }

            .school-major {
              display: flex;
              gap: 10px;
            }
          }

          .progress-status {
            margin-left: auto;
          }
        }
      }
    }
  }

  // 流程完成提示样式
  .process-completed-notice {
    margin-bottom: 20px;

    :deep(.el-alert) {
      border-radius: 8px;
      border: 1px solid #67c23a;
      background: linear-gradient(135deg, #f0f9ff 0%, #e8f5e8 100%);

      .el-alert__icon {
        color: #67c23a;
        font-size: 18px;
      }

      .el-alert__title {
        color: #409eff;
        font-weight: 600;
        font-size: 15px;
      }

      .el-alert__description {
        color: #67c23a;
        font-weight: 500;
        margin-top: 4px;
      }
    }
  }

  .progress-steps {
    margin-bottom: 25px;

    .steps-container {
      display: flex;
      align-items: center;
      position: relative;
    }

    .step-nav-btn {
      flex-shrink: 0;
      z-index: 10;

      &.prev-btn {
        margin-right: 8px; // 减少间距以节省空间
      }

      &.next-btn {
        margin-left: 8px; // 减少间距以节省空间
      }
    }

    .steps-wrapper {
      flex: 1;
      overflow: hidden;
      position: relative;
    }

    .steps-content {
      margin: 10px 5px; // 减少左右边距以节省空间
      display: flex;
      transition: transform 0.3s ease;
      position: relative;
      min-width: fit-content; // 确保内容不会被压缩
    }

    .step-item {
      flex-shrink: 0;
      width: 180px;
      padding: 12px 6px; // 减少内边距以节省空间
      margin: 0 6px; // 减少左右间距以在1000px宽度下显示更多步骤
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      cursor: default;
      border-radius: 12px;
      transition: all 0.3s ease;
      background: linear-gradient(145deg, #ffffff, #f8f9fa);
      border: 1px solid #e4e7ed;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      &.step-clickable {
        cursor: pointer;

        &:hover {
          background: linear-gradient(145deg, #f0f2f5, #e8eaed);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }
      }

      // 选中状态 - 最高优先级，明显区分
      &.step-active {
        background: linear-gradient(145deg, #e6f7ff, #cce7ff);
        border: 3px solid #409eff;
        box-shadow: 0 6px 20px rgba(64, 158, 255, 0.3);
        transform: scale(1.08); // 稍微增大缩放比例，更明显
        z-index: 10;

        .step-content {
          .step-title {
            color: #409eff;
            font-weight: 600;
          }

          .step-description {
            color: #337ecc;
            font-weight: 500;
          }
        }

        .step-icon {
          border: 2px solid #409eff;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4) !important;
          background: #ffffff !important; // 确保选中状态下图标背景是白色

          .step-number {
            color: #409eff !important; // 确保选中状态下数字是蓝色
            font-weight: 700 !important; // 加粗数字，提高可读性
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8); // 添加白色文字阴影增强对比度
          }
        }
      }

      // 当前步骤状态
      &.step-current:not(.step-active) {
        background: linear-gradient(145deg, #f0f9ff, #e1f3ff);
        border: 2px solid #409eff;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        animation: pulse 2s infinite;
      }

      // 已完成状态
      &.step-completed:not(.step-active) {
        background: linear-gradient(145deg, #f0f9ff, #e8f5e8);
        border: 1px solid #67c23a;
        box-shadow: 0 2px 8px rgba(103, 194, 58, 0.1);
      }

      // 已跳过状态
      &.step-skipped:not(.step-active) {
        background: linear-gradient(145deg, #fdf6ec, #faecd8);
        border: 1px solid #e6a23c;
        box-shadow: 0 2px 8px rgba(230, 162, 60, 0.1);
      }

      // 未开始状态 - 默认样式，较为低调
      &:not(.step-active):not(.step-current):not(.step-completed):not(.step-skipped) {
        background: linear-gradient(145deg, #fafbfc, #f0f2f5);
        border: 1px solid #dcdfe6;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        opacity: 0.8;

        .step-content {
          .step-title {
            color: #909399;
          }

          .step-description {
            color: #c0c4cc;
          }
        }
      }

      .step-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        margin-bottom: 10px;
        position: relative;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

        .step-number {
          font-size: 18px;
          font-weight: bold;
          z-index: 1;
          color: inherit;
        }

        .step-status-icon {
          position: absolute;
          font-size: 16px;
          z-index: 2;
          top: -2px;
          right: -2px;
          background: white;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        &.step-completed {
          background: linear-gradient(145deg, #67c23a, #5daf34);
          color: white;
          box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);

          .step-status-icon {
            background: #f0f9ff;
            color: #67c23a;
          }
        }

        &.step-current {
          background: linear-gradient(145deg, #409eff, #337ecc);
          color: white;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          animation: iconPulse 2s infinite;
        }

        &.step-skipped {
          background: linear-gradient(145deg, #e6a23c, #d48806);
          color: white;
          box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);

          .step-status-icon {
            background: #fdf6ec;
            color: #e6a23c;
          }
        }

        &.step-waiting {
          background: linear-gradient(145deg, #f0f2f5, #dcdfe6);
          color: #606266;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        }
      }

      .step-content {
        text-align: center;
        flex: 1;
        transition: all 0.3s ease;

        .step-title {
          font-size: 12px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
          line-height: 1.2;
          transition: all 0.3s ease;
        }

        .step-description {
          font-size: 11px;
          color: #909399;
          line-height: 1.2;
          transition: all 0.3s ease;
        }
      }

      .step-connector {
        position: absolute;
        right: -10px;
        top: 50%;
        transform: translateY(-50%);
        width: 20px;
        height: 3px;
        border-radius: 2px;
        transition: all 0.3s ease;

        &.connector-completed {
          background: linear-gradient(90deg, #67c23a, #5daf34);
          box-shadow: 0 1px 3px rgba(103, 194, 58, 0.3);
        }

        &.connector-skipped {
          background: linear-gradient(90deg, #e6a23c, #d48806);
          box-shadow: 0 1px 3px rgba(230, 162, 60, 0.3);
        }

        &.connector-default {
          background: linear-gradient(90deg, #dcdfe6, #c0c4cc);
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
      }

      &:last-child .step-connector {
        display: none;
      }
    }
  }

  .main-content {
    .content-tabs {
      :deep(.el-tabs__content) {
        padding: 15px 0;
      }
    }

    .operation-card {
      margin-top: 20px;

      :deep(.el-card__body) {
        padding: 20px;
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .email-content,
    .logs-content {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 15px;
      min-height: 500px;
      max-height: 600px;
      overflow-y: auto;

      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }
  }
}

// 对话框标题样式
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .dialog-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.custom-dialog {
  .el-dialog__body {
    padding: 15px 20px;
  }

  .el-dialog__header {
    padding: 15px 20px;
    margin-right: 0;
    border-bottom: 1px solid #ebeef5;
  }

  .el-dialog__footer {
    padding: 10px 20px;
    border-top: 1px solid #ebeef5;
    width: 100%;
  }

  &.el-dialog--fullscreen {
    .el-dialog__body {
      overflow: auto;
    }
  }
}

// 底部操作区样式
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.custom-dialog-btn {
  margin-left: 8px;
}

:deep(.el-tabs__content) {
  padding: 15px 0;
}

:deep(.el-tab-pane) {
  min-height: 300px;
}

// 动画效果
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }

  50% {
    transform: scale(1.02);
    box-shadow: 0 6px 16px rgba(64, 158, 255, 0.25);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }
}

@keyframes iconPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }

  50% {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .custom-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 0 auto;
    }
  }

  .progress-steps {
    .step-item {
      width: 160px;

      .step-icon {
        width: 40px;
        height: 40px;

        .step-number {
          font-size: 16px;
        }
      }

      .step-content {
        .step-title {
          font-size: 11px;
        }

        .step-description {
          font-size: 10px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .custom-dialog {
    .el-dialog {
      width: 98% !important;
      margin: 0 auto;
    }
  }

  .progress-steps {
    .step-item {
      width: 140px;
      padding: 8px 4px;

      .step-icon {
        width: 36px;
        height: 36px;

        .step-number {
          font-size: 14px;
        }

        .step-status-icon {
          font-size: 16px;
        }
      }

      .step-content {
        .step-title {
          font-size: 10px;
        }

        .step-description {
          font-size: 9px;
        }
      }
    }
  }

  .student-info-header {
    .student-header-content {
      flex-direction: column;
      gap: 10px;

      .student-basic {
        flex-direction: column;
        gap: 10px;
        text-align: center;

        .progress-status {
          margin-left: 0;
        }
      }
    }
  }
}
</style>

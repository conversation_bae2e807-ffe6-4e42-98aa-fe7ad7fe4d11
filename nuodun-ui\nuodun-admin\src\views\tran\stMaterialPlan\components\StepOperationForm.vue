<template>
  <div class="step-operation-form">
    <!-- 步骤表单内容 -->
    <div class="step-form-content">
      <component :is="currentStepComponent" v-if="currentStepComponent" v-model="stepData" :readonly="readonly"
        :progress-data="progressData" :step-config="stepConfig" @jumpOptionSelect="handleJumpOptionSelect"
        ref="stepFormRef" />
    </div>

    <!-- 跳转选择区域（如果该步骤有跳转选项且不是只读模式） -->
    <div v-if="hasJumpOptions && !readonly" class="jump-selection">
      <el-divider content-position="left">
        <span class="jump-title">流程分叉选择</span>
      </el-divider>
      <el-alert title="此步骤完成后可以选择不同的后续流程" description="请根据实际情况选择合适的流程路径" type="info" :closable="false" show-icon />

      <div class="jump-options">
        <el-radio-group v-model="selectedJumpOptionKey" @change="handleJumpOptionChange">
          <el-radio v-for="option in stepConfig.jumpOptions" :key="option.key" :value="option.key"
            class="jump-option-radio">
            <div class="jump-option-content">
              <div class="jump-option-label">{{ option.label }}</div>
              <div class="jump-option-description">{{ option.description }}</div>
            </div>
          </el-radio>
        </el-radio-group>
      </div>

      <div v-if="selectedJumpOption" class="jump-selection-info">
        <p><strong>当前选择：</strong>{{ selectedJumpOption.label }}</p>
        <p><strong>说明：</strong>{{ selectedJumpOption.description }}</p>
        <p v-if="selectedJumpOption.skipSteps && selectedJumpOption.skipSteps.length > 0">
          <strong>将跳过的步骤：</strong>{{ getSkippedStepsText(selectedJumpOption.skipSteps) }}
        </p>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="!readonly" class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button v-if="canSkipStep" type="warning" @click="handleSkip" :loading="submitting">
        跳过此步骤
      </el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        完成当前步骤
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, shallowRef, getCurrentInstance } from 'vue'
import { PROGRESS_STEPS, getStepInfo, STEP_DATA_TEMPLATES } from '@/utils/progressStepConstants'

const { proxy } = getCurrentInstance()

// 动态导入步骤组件 - 更新为12步骤
const stepComponents = {
  WRITING_PROCESS: () => import('./stepForms/WritingProcessStep.vue'),
  WRITING_FINALIZATION: () => import('./stepForms/WritingFinalizationStep.vue'),
  APPLICATION_FILLING: () => import('./stepForms/ApplicationFillingStep.vue'),
  INTERVIEW_INVITATION: () => import('./stepForms/InterviewInvitationStep.vue'),
  INTERVIEW_TRAINING: () => import('./stepForms/InterviewTrainingStep.vue'),
  ATTEND_INTERVIEW: () => import('./stepForms/AttendInterviewStep.vue'),
  ADMISSION_NOTICE: () => import('./stepForms/AdmissionNoticeStep.vue'),
  ACCEPT_OFFER_DEPOSIT: () => import('./stepForms/AcceptOfferDepositStep.vue'),
  VISA_PREPARATION: () => import('./stepForms/VisaPreparationStep.vue'),
  CONDITIONAL_MATERIALS: () => import('./stepForms/ConditionalMaterialsStep.vue'),
  VISA_FORMAL_OFFER: () => import('./stepForms/VisaFormalOfferStep.vue'),
  ACCOMMODATION_REGISTRATION: () => import('./stepForms/AccommodationRegistrationStep.vue')
}

const props = defineProps({
  step: {
    type: Object,
    required: true
  },
  progressData: {
    type: Object,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'endProcess', 'clearForm'])

const stepFormRef = ref(null)
const submitting = ref(false)
const currentStepComponent = shallowRef(null)
const selectedJumpOptionKey = ref(null)

// 获取步骤配置信息
const stepConfig = computed(() => {
  // 优先从progress数据的stepsConfig中获取
  if (props.progressData.stepsConfig) {
    try {
      const stepsConfig = typeof props.progressData.stepsConfig === 'string'
        ? JSON.parse(props.progressData.stepsConfig)
        : props.progressData.stepsConfig

      if (stepsConfig.steps && stepsConfig.steps[props.step.code]) {
        return {
          ...props.step,
          ...stepsConfig.steps[props.step.code]
        }
      }
    } catch (error) {
      console.warn('解析stepsConfig失败:', error)
    }
  }

  // 回退到常量定义
  return props.step
})

// 步骤数据 - 从数据库或模板初始化
const stepData = reactive({})

// 初始化步骤数据
const initializeStepData = () => {
  // 尝试从数据库中获取已保存的步骤数据
  if (props.progressData.stepsData) {
    try {
      const stepsData = typeof props.progressData.stepsData === 'string'
        ? JSON.parse(props.progressData.stepsData)
        : props.progressData.stepsData

      if (stepsData[props.step.code]) {
        Object.assign(stepData, stepsData[props.step.code])
        return
      }
    } catch (error) {
      console.warn('解析stepsData失败:', error)
    }
  }

  // 使用模板数据初始化
  const template = STEP_DATA_TEMPLATES[props.step.code] || {}
  Object.assign(stepData, template)
}

// 是否有跳转选项
const hasJumpOptions = computed(() => {
  return stepConfig.value.jumpOptions && stepConfig.value.jumpOptions.length > 0
})

// 当前选中的跳转选项
const selectedJumpOption = computed(() => {
  if (!selectedJumpOptionKey.value || !stepConfig.value.jumpOptions) return null
  return stepConfig.value.jumpOptions.find(opt => opt.key === selectedJumpOptionKey.value)
})

// 是否可以跳过当前步骤
const canSkipStep = computed(() => {
  const stepInfo = getStepInfo(props.step.code)
  return stepInfo.canSkip === true
})

// 获取跳过的步骤文本
const getSkippedStepsText = (skipSteps) => {
  return skipSteps.map(stepCode => {
    const stepInfo = getStepInfo(stepCode)
    return stepInfo.name || stepCode
  }).join('、')
}

// 加载对应的步骤组件
const loadStepComponent = async () => {
  if (stepComponents[props.step.code]) {
    try {
      const component = await stepComponents[props.step.code]()
      currentStepComponent.value = component.default
    } catch (error) {
      console.error(`Failed to load step component: ${props.step.code}`, error)
      proxy.$modal.msgError('步骤组件加载失败')
    }
  } else {
    console.warn(`No component found for step: ${props.step.code}`)
  }
}

// 处理跳转选项选择
const handleJumpOptionSelect = (option) => {
  selectedJumpOptionKey.value = option.key
}

// 处理跳转选项改变
const handleJumpOptionChange = (value) => {
  // 可以在这里添加额外的逻辑
}

// 监听步骤变化，重新加载组件
watch(() => props.step.code, () => {
  loadStepComponent()
  initializeStepData()
  selectedJumpOptionKey.value = null
}, { immediate: true })

// 验证表单
const validateForm = async () => {
  if (!stepFormRef.value) return true

  try {
    if (typeof stepFormRef.value.validate === 'function') {
      return await stepFormRef.value.validate()
    }
    return true
  } catch (error) {
    console.error('Form validation error:', error)
    return false
  }
}

// 提交表单
const handleSubmit = async () => {
  submitting.value = true

  try {
    // 验证表单
    const isValid = await validateForm()
    if (!isValid) {
      proxy.$modal.msgError('请完善表单信息')
      return
    }

    // 准备提交数据
    const submitData = {
      stepCode: props.step.code,
      stepData: {
        ...stepData,
        operationTime: new Date().toISOString(),
        remark: stepData.remark || ''
      },
      jumpOption: selectedJumpOption.value,
      stepStatus: 'COMPLETED'
    }

    // 触发提交事件
    emit('submit', submitData)

  } catch (error) {
    console.error('Submit error:', error)
    proxy.$modal.msgError('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 跳过步骤
const handleSkip = async () => {
  submitting.value = true

  try {
    const submitData = {
      stepCode: props.step.code,
      stepData: {
        ...stepData,
        operationTime: new Date().toISOString(),
        remark: stepData.remark || '跳过此步骤'
      },
      jumpOption: null,
      stepStatus: 'SKIPPED'
    }

    emit('submit', submitData)

  } catch (error) {
    console.error('Skip error:', error)
    proxy.$modal.msgError('跳过操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('clearForm')
}

// 重置表单
const resetForm = () => {
  if (stepFormRef.value && typeof stepFormRef.value.resetForm === 'function') {
    stepFormRef.value.resetForm()
  }
  initializeStepData()
  selectedJumpOptionKey.value = null
}

defineExpose({
  resetForm,
  validateForm
})
</script>

<style scoped lang="scss">
.step-operation-form {
  .step-form-content {
    margin-bottom: 24px;
  }

  .jump-selection {
    margin: 24px 0;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 1px solid #dee2e6;

    .jump-title {
      font-weight: 600;
      color: #303133;
      font-size: 16px;
    }

    .el-alert {
      margin: 16px 0 20px 0;
    }

    .jump-options {
      margin: 20px 0;

      .jump-option-radio {
        display: block;
        margin-bottom: 16px;
        padding: 16px;
        background: white;
        border-radius: 6px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        &.is-checked {
          border-color: #409eff;
          background: #f0f9ff;
          box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
        }

        :deep(.el-radio__input) {
          margin-top: 4px;
        }

        .jump-option-content {
          margin-left: 24px;

          .jump-option-label {
            font-weight: 600;
            color: #303133;
            font-size: 15px;
            margin-bottom: 4px;
          }

          .jump-option-description {
            color: #606266;
            font-size: 14px;
            line-height: 1.4;
          }
        }
      }
    }

    .jump-selection-info {
      margin-top: 20px;
      padding: 18px;
      background: white;
      border-radius: 6px;
      border: 1px solid #dcdfe6;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      p {
        margin: 12px 0;
        line-height: 1.6;
        font-size: 14px;

        strong {
          color: #303133;
          font-weight: 600;
        }

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 24px;
    border-top: 1px solid #ebeef5;

    .el-button {
      min-width: 100px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .step-operation-form {
    .jump-selection {
      padding: 16px;

      .jump-options {
        .jump-option-radio {
          padding: 12px;

          .jump-option-content {
            margin-left: 20px;

            .jump-option-label {
              font-size: 14px;
            }

            .jump-option-description {
              font-size: 13px;
            }
          }
        }
      }
    }

    .form-actions {
      flex-direction: column;
      gap: 8px;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>

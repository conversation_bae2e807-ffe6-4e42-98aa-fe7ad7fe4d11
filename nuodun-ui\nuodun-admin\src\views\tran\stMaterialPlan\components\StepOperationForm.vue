<template>
  <div class="step-operation-form">
    <!-- 步骤表单内容 -->
    <div class="step-form-content">
      <component :is="currentStepComponent" v-model="stepData" :readonly="readonly" :showJumpOptions="showJumpOptions"
        @jumpOptionSelect="handleJumpOptionSelect" ref="stepFormRef" />
    </div>

    <!-- 跳转选择区域（如果该步骤有跳转选项且不是只读模式） -->
    <div v-if="hasJumpOptions && !readonly" class="jump-selection">
      <el-divider content-position="left">
        <span class="jump-title">流程分叉选择</span>
      </el-divider>
      <el-alert title="此步骤完成后可以选择不同的后续流程" description="请根据实际情况选择合适的流程路径" type="info" :closable="false" show-icon />
      <div class="jump-selection-info">
        <p><strong>当前选择：</strong>{{ selectedJumpOption?.label || '默认流程' }}</p>
        <p><strong>说明：</strong>{{ selectedJumpOption?.description || '按标准流程继续' }}</p>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="!readonly" class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        完成当前步骤
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, shallowRef } from 'vue'

// 动态导入步骤组件
const stepComponents = {
  WRITING_PROCESS: () => import('./stepForms/WritingProcessStep.vue'),
  WRITING_FINALIZATION: () => import('./stepForms/WritingFinalizationStep.vue'),
  APPLICATION_FILLING: () => import('./stepForms/ApplicationFillingStep.vue'),
  INTERVIEW_INVITATION: () => import('./stepForms/InterviewInvitationStep.vue'),
  INTERVIEW_TRAINING: () => import('./stepForms/InterviewTrainingStep.vue'),
  ATTEND_INTERVIEW: () => import('./stepForms/AttendInterviewStep.vue'),
  ADMISSION_NOTICE: () => import('./stepForms/AdmissionNoticeStep.vue'),
  ACCEPT_OFFER_DEPOSIT: () => import('./stepForms/AcceptOfferDepositStep.vue'),
  VISA_PREPARATION: () => import('./stepForms/VisaPreparationStep.vue'),
  CONDITIONAL_MATERIALS: () => import('./stepForms/ConditionalMaterialsStep.vue'),
  VISA_FORMAL_OFFER: () => import('./stepForms/VisaFormalOfferStep.vue'),
  ACCOMMODATION_REGISTRATION: () => import('./stepForms/AccommodationRegistrationStep.vue')
}

const props = defineProps({
  stepCode: {
    type: String,
    required: true
  },
  stepConfig: {
    type: Object,
    required: true
  },
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  },
  progressId: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'complete', 'cancel'])

const stepFormRef = ref(null)
const submitting = ref(false)
const currentStepComponent = shallowRef(null)
const selectedJumpOption = ref(null)

// 步骤数据
const stepData = reactive({
  ...props.modelValue
})

// 是否有跳转选项
const hasJumpOptions = computed(() => {
  return props.stepConfig?.jumpOptions && props.stepConfig.jumpOptions.length > 0
})

// 是否显示跳转选项（对于有跳转选项的步骤）
const showJumpOptions = computed(() => {
  return hasJumpOptions.value && !props.readonly
})

// 步骤配置映射
const stepConfigMap = {
  WRITING_PROCESS: {
    name: '文书环节',
    canJump: false
  },
  WRITING_FINALIZATION: {
    name: '文书定稿',
    canJump: false
  },
  APPLICATION_FILLING: {
    name: '学校申请填写',
    canJump: true,
    jumpOptions: [
      { key: 'normal', label: '正常流程（包含面试）', nextStep: 'INTERVIEW_INVITATION' },
      { key: 'direct_admission', label: '直接录取（跳过面试）', nextStep: 'ADMISSION_NOTICE' }
    ]
  },
  INTERVIEW_INVITATION: {
    name: '面试邀请通知',
    canJump: false
  },
  INTERVIEW_TRAINING: {
    name: '面试培训安排',
    canJump: false
  },
  ATTEND_INTERVIEW: {
    name: '参加面试',
    canJump: false
  },
  ADMISSION_NOTICE: {
    name: '录取通知',
    canJump: true,
    jumpOptions: [
      { key: 'conditional', label: '有条件录取流程', nextStep: 'ACCEPT_OFFER_DEPOSIT' },
      { key: 'unconditional', label: '无条件录取流程（跳过条件材料）', nextStep: 'ACCEPT_OFFER_DEPOSIT' }
    ]
  },
  ACCEPT_OFFER_DEPOSIT: {
    name: '接受录取&留位费',
    canJump: false
  },
  VISA_PREPARATION: {
    name: '签证申请准备',
    canJump: true,
    jumpOptions: [
      { key: 'normal', label: '正常签证流程', nextStep: 'CONDITIONAL_MATERIALS' },
      { key: 'no_visa_required', label: '无需签证（本地学生）', nextStep: 'VISA_FORMAL_OFFER' }
    ]
  },
  CONDITIONAL_MATERIALS: {
    name: '条件录取材料递交',
    canJump: false
  },
  VISA_FORMAL_OFFER: {
    name: '签证审批&正式录取',
    canJump: false
  },
  ACCOMMODATION_REGISTRATION: {
    name: '住宿申请&注册',
    canJump: false
  }
}

// 加载对应的步骤组件
const loadStepComponent = async () => {
  if (stepComponents[props.stepCode]) {
    try {
      const component = await stepComponents[props.stepCode]()
      currentStepComponent.value = component.default
    } catch (error) {
      console.error(`Failed to load step component: ${props.stepCode}`, error)
      proxy.$modal.msgError('步骤组件加载失败');
    }
  }
}

// 处理跳转选项选择
const handleJumpOptionSelect = (option) => {
  selectedJumpOption.value = option
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(stepData, newValue)
}, { immediate: true })

// 监听步骤代码变化，重新加载组件
watch(() => props.stepCode, () => {
  loadStepComponent()
  selectedJumpOption.value = null
}, { immediate: true })

// 验证表单
const validateForm = async () => {
  if (!stepFormRef.value) return true

  try {
    if (typeof stepFormRef.value.validate === 'function') {
      return await stepFormRef.value.validate()
    }
    return true
  } catch (error) {
    console.error('Form validation error:', error)
    return false
  }
}

// 获取跳转选项
const getJumpOption = () => {
  if (!hasJumpOptions.value) return null

  if (stepFormRef.value && typeof stepFormRef.value.getCurrentJumpOption === 'function') {
    return stepFormRef.value.getCurrentJumpOption()
  }

  return selectedJumpOption.value
}

// 提交表单
const handleSubmit = async () => {
  submitting.value = true

  try {
    // 验证表单
    const isValid = await validateForm()
    if (!isValid) {
      proxy.$modal.msgError('请完善表单信息')
      return
    }

    // 准备提交数据
    const submitData = {
      stepCode: props.stepCode,
      stepData: { ...stepData },
      jumpOption: getJumpOption()
    }

    // 触发完成事件
    emit('complete', submitData)

  } catch (error) {
    console.error('Submit error:', error)
    proxy.$modal.msgError('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}

// 重置表单
const resetForm = () => {
  if (stepFormRef.value && typeof stepFormRef.value.resetForm === 'function') {
    stepFormRef.value.resetForm()
  }
  selectedJumpOption.value = null
}

defineExpose({
  resetForm,
  validateForm
})
</script>

<style scoped lang="scss">
.step-operation-form {
  .step-form-content {
    margin-bottom: 24px;
  }

  .jump-selection {
    margin: 24px 0;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .jump-title {
      font-weight: 600;
      color: #303133;
    }

    .el-alert {
      margin: 16px 0;
    }

    .jump-selection-info {
      margin-top: 16px;
      padding: 12px;
      background: #fff;
      border-radius: 4px;
      border: 1px solid #dcdfe6;

      p {
        margin: 8px 0;
        line-height: 1.5;

        strong {
          color: #303133;
        }
      }
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 24px;
    border-top: 1px solid #ebeef5;
  }
}
</style>

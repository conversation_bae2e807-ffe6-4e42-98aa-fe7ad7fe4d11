package com.nuodun.web.controller.tran;

import com.nuodun.common.annotation.Log;
import com.nuodun.common.core.controller.BaseController;
import com.nuodun.common.core.domain.AjaxResult;
import com.nuodun.common.core.page.TableDataInfo;
import com.nuodun.common.enums.BusinessType;
import com.nuodun.common.utils.SecurityUtils;
import com.nuodun.tran.domain.TStMaterialPlanProgress;
import com.nuodun.tran.domain.dto.UpdateProgressDto;
import com.nuodun.tran.domain.entity.TStMaterialPlanProgressLogEntity;
import com.nuodun.tran.service.TStMaterialPlanProgressLogService;
import com.nuodun.tran.service.TStMaterialPlanProgressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 专业申请流程进度控制器
 * 提供申请进度的核心API接口：查询、初始化、更新操作
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/tran/materialPlanProgress")
public class TStMaterialPlanProgressController extends BaseController {

    @Autowired
    private TStMaterialPlanProgressService progressService;

    @Autowired
    private TStMaterialPlanProgressLogService logService;

    /**
     * 根据专业计划ID获取进度信息
     */
    @GetMapping("/getByPlanId/{planId}")
    public AjaxResult getByPlanId(@PathVariable Long planId) {
        TStMaterialPlanProgress progress = progressService.getByPlanId(planId);
        return success(progress);
    }

    /**
     * 根据学生ID获取所有进度信息
     */
    @GetMapping("/getByStId/{stId}")
    public AjaxResult getByStId(@PathVariable Long stId) {
        List<TStMaterialPlanProgress> progressList = progressService.getByStId(stId);
        return success(progressList);
    }

    /**
     * 初始化专业申请进度
     */
    @Log(title = "初始化申请进度", businessType = BusinessType.INSERT)
    @PostMapping("/initProgress")
    public AjaxResult initProgress(@RequestBody TStMaterialPlanProgress progress) {
        TStMaterialPlanProgress result = progressService.initProgress(
                progress.getPlanId(),
                progress.getStId(),
                progress.getContractId(),
                progress.getOrderId(),
                progress.getStDecaType(),
                progress.getApplySchool(),
                progress.getApplyMajorZh(),
                progress.getApplyMajorEn()
        );
        return success(result);
    }

    /**
     * 统一更新进度信息
     * 根据传入的参数类型判断更新哪些数据
     */
    @Log(title = "更新进度信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updateProgress/{progressId}")
    public AjaxResult updateProgress(@PathVariable Long progressId, @RequestBody UpdateProgressDto updateData) {
        boolean result = progressService.updateProgress(progressId, updateData, SecurityUtils.getUserId());
        return toAjax(result);
    }

    /**
     * 获取操作日志
     */
    @GetMapping("/getLogs/{progressId}")
    public TableDataInfo getLogs(@PathVariable Long progressId) {
        startPage();
        List<TStMaterialPlanProgressLogEntity> list = logService.getByProgressId(progressId);
        return getDataTable(list);
    }

}

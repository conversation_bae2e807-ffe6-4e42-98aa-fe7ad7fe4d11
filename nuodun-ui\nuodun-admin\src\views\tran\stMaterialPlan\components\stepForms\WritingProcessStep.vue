<template>
  <div class="writing-process-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>文书环节详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>是否需要文书约谈：</label>
            <span>{{ stepData.needsConsultation ? '是' : '否' }}</span>
          </div>
          <div v-if="stepData.needsConsultation" class="info-item">
            <label>文书老师：</label>
            <span>{{ stepData.writingTeacher || '暂未分配' }}</span>
          </div>
          <div v-if="stepData.consultationTime" class="info-item">
            <label>文书约谈时间：</label>
            <span>{{ stepData.consultationTime }}</span>
          </div>
          <div v-if="stepData.remark" class="info-item">
            <label>备注说明：</label>
            <span>{{ stepData.remark }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="是否需要文书约谈" prop="needsConsultation">
          <el-radio-group v-model="stepData.needsConsultation">
            <el-radio :label="true">需要约谈</el-radio>
            <el-radio :label="false">不需要约谈</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="stepData.needsConsultation">
          <el-form-item label="文书老师" prop="writingTeacher">
            <el-input v-model="stepData.writingTeacher" placeholder="请输入文书老师姓名" />
          </el-form-item>

          <el-form-item label="文书约谈时间" prop="consultationTime">
            <el-date-picker
              v-model="stepData.consultationTime"
              type="datetime"
              placeholder="请选择文书约谈时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </template>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="2"
            placeholder="其他备注信息"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据 - 按照新的JSON字段配置
const stepData = reactive({
  needsConsultation: false,        // 是否需要文书约谈
  writingTeacher: '',              // 文书老师姓名
  consultationTime: '',            // 文书约谈时间
  remark: ''                       // 备注说明
})

// 验证规则
const rules = {
  needsConsultation: [
    { required: true, message: '请选择是否需要文书约谈', trigger: 'change' }
  ],
  writingTeacher: [
    { 
      validator: (rule, value, callback) => {
        if (stepData.needsConsultation && !value) {
          callback(new Error('请输入文书老师姓名'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  consultationTime: [
    { 
      validator: (rule, value, callback) => {
        if (stepData.needsConsultation && !value) {
          callback(new Error('请选择文书约谈时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    needsConsultation: false,
    writingTeacher: '',
    consultationTime: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    needsConsultation: false,
    writingTeacher: '',
    consultationTime: '',
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.writing-process-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 140px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }
  }
}
</style> 
/*
 Navicat Premium Data Transfer

 Source Server         : 本机80
 Source Server Type    : MySQL
 Source Server Version : 80030
 Source Host           : localhost:3306
 Source Schema         : nuodun-ruoyi

 Target Server Type    : MySQL
 Target Server Version : 80030
 File Encoding         : 65001

 Date: 30/07/2025 10:30:00
 Updated: 重新设计表结构，将步骤信息拆分到独立表中，按照12个主要步骤设计
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_st_material_plan_progress_main (主表)
-- ----------------------------
DROP TABLE IF EXISTS `t_st_material_plan_progress`;
CREATE TABLE `t_st_material_plan_progress`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_id` bigint(0) NOT NULL COMMENT '专业计划ID(关联t_st_material_plan.id)',
  `st_id` bigint(0) NOT NULL COMMENT '学生ID',
  `contract_id` bigint(0) NOT NULL COMMENT '合同主表ID',
  `order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单ID',
  `st_deca_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学生类型',
  `apply_school` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请学校',
  `apply_major_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请专业中文名',
  `apply_major_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请专业英文名',
  `current_step` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'WRITING_PROCESS' COMMENT '当前步骤编码',
  `current_step_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '文书环节' COMMENT '当前步骤名称',
  `progress_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'IN_PROGRESS' COMMENT '整体进度状态：IN_PROGRESS进行中 COMPLETED已完成 CANCELLED取消',
  
  -- =========================
  -- 步骤配置数据（JSON格式）
  -- =========================
  `steps_config` json NULL COMMENT '步骤配置数据：JSON格式存储该专业的所有步骤信息，包括步骤编码、名称、说明、排序、是否必须等',
  
  -- =========================
  -- 业务字段
  -- =========================
  `application_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请编号',
  `application_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请邮箱',
  `application_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请密码',
  `application_submission_time` datetime(0) NULL DEFAULT NULL COMMENT '申请递交时间',
  `interview_link` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '面试链接',
  `interview_date_time` datetime(0) NULL DEFAULT NULL COMMENT '面试日期时间',
  
  -- 录取通知相关字段
  `is_receive_admission` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否收到录取通知：Y已收到 N未收到',
  `receive_admission_time` datetime(0) NULL DEFAULT NULL COMMENT '收到录取通知时间',
  `admission_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '录取类型：CONDITIONAL有条件录取 UNCONDITIONAL无条件录取',
  `admission_conditions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '录取条件',
  
  -- 留位费相关字段
  `deposit_deadline` datetime(0) NULL DEFAULT NULL COMMENT '留位费截止时间',
  `deposit_amount` decimal(11, 2) NULL DEFAULT NULL COMMENT '留位费金额',
  `is_pay_deposit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否缴纳留位费：Y已缴纳 N未缴纳',
  `pay_deposit_time` datetime(0) NULL DEFAULT NULL COMMENT '缴纳留位费时间',
  
  -- 签证相关字段
  `visa_admin_fee` decimal(11, 2) NULL DEFAULT NULL COMMENT '签证行政费金额',
  `is_pay_visa_fee` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否缴纳签证行政费：Y已缴纳 N未缴纳',
  `pay_visa_fee_time` datetime(0) NULL DEFAULT NULL COMMENT '缴纳签证行政费时间',
  `visa_approval_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '签证审批状态：PENDING待审批 APPROVED已通过 REJECTED已拒绝',
  `visa_approval_time` datetime(0) NULL DEFAULT NULL COMMENT '签证审批时间',
  
  -- 正式录取相关字段  
  `is_receive_formal_offer` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否收到正式录取：Y已收到 N未收到',
  `receive_formal_offer_time` datetime(0) NULL DEFAULT NULL COMMENT '收到正式录取时间',
  
  -- 注册相关字段
  `accommodation_application` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否申请住宿：Y已申请 N未申请',
  `accommodation_application_time` datetime(0) NULL DEFAULT NULL COMMENT '住宿申请时间',
  `is_confirm_enrollment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否确认就读：Y已确认 N未确认',
  `confirm_enrollment_time` datetime(0) NULL DEFAULT NULL COMMENT '确认就读时间',
  
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志：0代表存在 2代表删除',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_plan_id`(`plan_id`) USING BTREE,
  INDEX `idx_st_id`(`st_id`) USING BTREE,
  INDEX `idx_contract_id`(`contract_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_current_step`(`current_step`) USING BTREE,
  INDEX `idx_progress_status`(`progress_status`) USING BTREE,
  INDEX `idx_st_deca_type`(`st_deca_type`) USING BTREE,
  INDEX `idx_apply_school`(`apply_school`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '专业申请流程进度主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_st_material_plan_progress_steps (步骤表)
-- ----------------------------
DROP TABLE IF EXISTS `t_st_material_plan_progress_steps`;
CREATE TABLE `t_st_material_plan_progress_steps`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `progress_id` bigint(0) NOT NULL COMMENT '关联主表ID',
  `step_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '步骤编码',
  `step_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '步骤名称',
  `step_order` int(0) NOT NULL COMMENT '步骤排序',
  `step_phase` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '步骤阶段：PREPARATION申请准备 POST_APPLICATION申请后环节 POST_ADMISSION录取后流程 FINAL_CONFIRMATION最终确认',
  `step_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'PENDING' COMMENT '步骤状态：PENDING待处理 IN_PROGRESS进行中 COMPLETED完成 SKIPPED跳过 CANCELLED取消',
  `step_data` json NULL COMMENT '步骤数据：JSON格式存储业务数据',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志：0代表存在 2代表删除',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '修改人ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_progress_id`(`progress_id`) USING BTREE,
  INDEX `idx_step_code`(`step_code`) USING BTREE,
  INDEX `idx_step_order`(`step_order`) USING BTREE,
  INDEX `idx_step_phase`(`step_phase`) USING BTREE,
  INDEX `idx_step_status`(`step_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '专业申请流程步骤表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- steps_config字段JSON结构说明（12个主要步骤配置）
-- ----------------------------
/*
steps_config字段存储格式示例：
{
  "steps": [
    {
      "stepCode": "WRITING_PROCESS",
      "stepName": "文书环节", 
      "stepOrder": 1,
      "stepPhase": "PREPARATION",
      "stepDescription": "定校结果确定后，进入文书环节",
      "isRequired": false,
      "jsonTemplate": {
        "needsConsultation": false,
        "writingTeacher": "",
        "writingDirection": ""
      }
    },
    {
      "stepCode": "WRITING_FINALIZATION",
      "stepName": "文书定稿",
      "stepOrder": 2, 
      "stepPhase": "PREPARATION",
      "stepDescription": "文书内容确认和版本管理",
      "isRequired": true,
      "jsonTemplate": {
        "finalFileList": ""
      }
    },
    {
      "stepCode": "APPLICATION_FILLING",
      "stepName": "学校申请填写",
      "stepOrder": 3,
      "stepPhase": "PREPARATION", 
      "stepDescription": "进行学校申请填写",
      "isRequired": true,
      "jsonTemplate": {
        "applicationNumber": "",
        "applicationEmail": "",
        "applicationPassword": ""
      }
    },
    {
      "stepCode": "INTERVIEW_INVITATION",
      "stepName": "面试邀请通知",
      "stepOrder": 4,
      "stepPhase": "POST_APPLICATION",
      "stepDescription": "部分学校会安排面试，升学老师沟通并安排面培",
      "isRequired": false,
      "jsonTemplate": {
        "interviewInvited": false,
        "interviewTime": ""
      }
    },
    {
      "stepCode": "INTERVIEW_TRAINING", 
      "stepName": "面试培训安排",
      "stepOrder": 5,
      "stepPhase": "POST_APPLICATION",
      "stepDescription": "升学老师沟通并安排面培",
      "isRequired": false,
      "jsonTemplate": {
        "trainingArranged": false
      }
    },
    {
      "stepCode": "ATTEND_INTERVIEW",
      "stepName": "参加面试",
      "stepOrder": 6,
      "stepPhase": "POST_APPLICATION",
      "stepDescription": "学生参加学校面试", 
      "isRequired": false,
      "jsonTemplate": {
        "interviewAttended": false,
        "interviewLink": ""
      }
    },
    {
      "stepCode": "ADMISSION_NOTICE",
      "stepName": "录取通知",
      "stepOrder": 7,
      "stepPhase": "POST_APPLICATION",
      "stepDescription": "收到录取通知（有条件录取或正式录取）",
      "isRequired": true,
      "jsonTemplate": {
        "admissionReceived": false,
        "admissionType": "CONDITIONAL",
        "noticeTime": "",
        "admissionConditions": "",
        "admissionDeadline": null
      }
    },
    {
      "stepCode": "ACCEPT_OFFER_DEPOSIT",
      "stepName": "接受录取&留位费",
      "stepOrder": 8,
      "stepPhase": "POST_ADMISSION", 
      "stepDescription": "录取后接受入读并缴纳留位费",
      "isRequired": true,
      "jsonTemplate": {
        "offerAccepted": false,
        "depositRequired": false,
        "depositAmount": "",
        "depositDeadline": "",
        "payDepositTime": ""
      }
    },
    {
      "stepCode": "VISA_PREPARATION",
      "stepName": "签证申请准备",
      "stepOrder": 9,
      "stepPhase": "POST_ADMISSION",
      "stepDescription": "缴纳留位费后，进入签证申请环节",
      "isRequired": false,
      "jsonTemplate": {
        "visaRequired": false,
        "formsFilled": "",
        "materialsList": [],
        "visaFeePaid": false,
        "visaFeeAmount": 0
      }
    },
    {
      "stepCode": "CONDITIONAL_MATERIALS",
      "stepName": "条件录取材料递交",
      "stepOrder": 10,
      "stepPhase": "FINAL_CONFIRMATION",
      "stepDescription": "获有条件录取的学生，需递交满足条件的学术材料等",
      "isRequired": false,
      "jsonTemplate": {
        "materialChecklist": "",
        "submissionTime": ""
      }
    },
    {
      "stepCode": "VISA_FORMAL_OFFER",
      "stepName": "签证审批&正式录取",
      "stepOrder": 11,
      "stepPhase": "FINAL_CONFIRMATION", 
      "stepDescription": "签证审批通过且所有条件满足后，获得正式录取",
      "isRequired": true,
      "jsonTemplate": {
        "isReceiveFormalOffer": false,
        "receiveFormalOfferTime": "",
        "formalOfferDetails": ""
      }
    },
    {
      "stepCode": "ACCOMMODATION_REGISTRATION",
      "stepName": "住宿申请&注册",
      "stepOrder": 12,
      "stepPhase": "FINAL_CONFIRMATION",
      "stepDescription": "住宿申请和学校注册", 
      "isRequired": true,
      "jsonTemplate": {
        "accommodationApplied": false,
        "accommodationTime": "",
        "registrationCompleted": false,
        "registrationTime": "",
        "enrollmentConfirmed": null
      }
    }
  ]
}
*/

SET FOREIGN_KEY_CHECKS = 1;

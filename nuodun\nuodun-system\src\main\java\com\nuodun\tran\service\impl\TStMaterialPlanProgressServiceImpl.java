package com.nuodun.tran.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nuodun.common.utils.DateUtils;
import com.nuodun.tran.domain.TStMaterialPlanProgress;
import com.nuodun.tran.domain.TStMaterialPlanProgressLog;
import com.nuodun.tran.domain.dto.UpdateProgressDto;
import com.nuodun.tran.mapper.TStMaterialPlanProgressMapper;
import com.nuodun.tran.service.TStMaterialPlanProgressLogService;
import com.nuodun.tran.service.TStMaterialPlanProgressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 专业申请流程进度Service实现类 - 重构版
 * 支持新的12步骤流程和数据库驱动的步骤配置
 *
 * <AUTHOR>
 * @description 针对表【t_st_material_plan_progress(专业申请流程进度表)】的数据库操作Service实现
 * @createDate 2025-07-30
 */
@Service
public class TStMaterialPlanProgressServiceImpl extends ServiceImpl<TStMaterialPlanProgressMapper, TStMaterialPlanProgress>
    implements TStMaterialPlanProgressService {

    @Autowired
    private TStMaterialPlanProgressMapper progressMapper;

    @Autowired
    private TStMaterialPlanProgressLogService logService;

    // 12步骤常量定义
    private static final String[] STEP_CODES = {
        "WRITING_PROCESS", "WRITING_FINALIZATION", "APPLICATION_FILLING",
        "INTERVIEW_INVITATION", "INTERVIEW_TRAINING", "ATTEND_INTERVIEW",
        "ADMISSION_NOTICE", "ACCEPT_OFFER_DEPOSIT", "VISA_PREPARATION",
        "CONDITIONAL_MATERIALS", "VISA_FORMAL_OFFER", "ACCOMMODATION_REGISTRATION"
    };

    private static final Map<String, String> STEP_NAMES = new HashMap<>();
    private static final Map<String, String> STEP_PHASES = new HashMap<>();
    private static final Map<String, Integer> STEP_ORDERS = new HashMap<>();

    static {
        STEP_NAMES.put("WRITING_PROCESS", "文书环节");
        STEP_NAMES.put("WRITING_FINALIZATION", "文书定稿");
        STEP_NAMES.put("APPLICATION_FILLING", "学校申请填写");
        STEP_NAMES.put("INTERVIEW_INVITATION", "面试邀请通知");
        STEP_NAMES.put("INTERVIEW_TRAINING", "面试培训安排");
        STEP_NAMES.put("ATTEND_INTERVIEW", "参加面试");
        STEP_NAMES.put("ADMISSION_NOTICE", "录取通知");
        STEP_NAMES.put("ACCEPT_OFFER_DEPOSIT", "接受录取&留位费");
        STEP_NAMES.put("VISA_PREPARATION", "签证申请准备");
        STEP_NAMES.put("CONDITIONAL_MATERIALS", "条件录取材料递交");
        STEP_NAMES.put("VISA_FORMAL_OFFER", "签证审批&正式录取");
        STEP_NAMES.put("ACCOMMODATION_REGISTRATION", "住宿申请&注册");

        // 步骤阶段定义
        STEP_PHASES.put("WRITING_PROCESS", "PREPARATION");
        STEP_PHASES.put("WRITING_FINALIZATION", "PREPARATION");
        STEP_PHASES.put("APPLICATION_FILLING", "PREPARATION");
        STEP_PHASES.put("INTERVIEW_INVITATION", "POST_APPLICATION");
        STEP_PHASES.put("INTERVIEW_TRAINING", "POST_APPLICATION");
        STEP_PHASES.put("ATTEND_INTERVIEW", "POST_APPLICATION");
        STEP_PHASES.put("ADMISSION_NOTICE", "POST_APPLICATION");
        STEP_PHASES.put("ACCEPT_OFFER_DEPOSIT", "POST_ADMISSION");
        STEP_PHASES.put("VISA_PREPARATION", "POST_ADMISSION");
        STEP_PHASES.put("CONDITIONAL_MATERIALS", "POST_ADMISSION");
        STEP_PHASES.put("VISA_FORMAL_OFFER", "POST_ADMISSION");
        STEP_PHASES.put("ACCOMMODATION_REGISTRATION", "FINAL_CONFIRMATION");

        // 步骤顺序定义
        for (int i = 0; i < STEP_CODES.length; i++) {
            STEP_ORDERS.put(STEP_CODES[i], i + 1);
        }
    }

    // =========================
    // 基础CRUD操作实现
    // =========================

    @Override
    public TStMaterialPlanProgress getByPlanId(Long planId) {
        return progressMapper.selectByPlanId(planId);
    }

    @Override
    public List<TStMaterialPlanProgress> getByStId(Long stId) {
        return progressMapper.selectByStId(stId);
    }

    @Override
    public List<TStMaterialPlanProgressLog> getLogs(Long progressId) {
        return logService.getByProgressId(progressId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TStMaterialPlanProgress initProgress(Long planId, Long stId, Long contractId, String orderId,
                                              String stDecaType, String applySchool, String applyMajorZh, String applyMajorEn) {
        // 检查是否已存在
        TStMaterialPlanProgress existing = getByPlanId(planId);
        if (existing != null) {
            return existing;
        }

        // 创建新的进度记录
        TStMaterialPlanProgress progress = new TStMaterialPlanProgress();
        progress.setPlanId(planId);
        progress.setStId(stId);
        progress.setContractId(contractId);
        progress.setOrderId(orderId);
        progress.setStDecaType(stDecaType);
        progress.setApplySchool(applySchool);
        progress.setApplyMajorZh(applyMajorZh);
        progress.setApplyMajorEn(applyMajorEn);
        progress.setCurrentStep("WRITING_PROCESS");
        progress.setCurrentStepName("文书环节");
        progress.setProgressStatus("IN_PROGRESS");
        progress.setDelFlag("0");
        progress.setCreateTime(new Date());

        // 初始化步骤配置
        String stepsConfig = createDefaultStepsConfig();
        progress.setStepsConfig(stepsConfig);

        // 初始化步骤数据
        String stepsData = createDefaultStepsData();
        progress.setStepsData(stepsData);

        // 保存到数据库
        progressMapper.insert(progress);

        // 记录操作日志
        logProgress(progress.getId(), "INIT", "初始化申请进度", "", null);

        return progress;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProgress(Long progressId, UpdateProgressDto updateData, Long operatorId) {
        TStMaterialPlanProgress progress = progressMapper.selectById(progressId);
        if (progress == null) {
            throw new RuntimeException("进度记录不存在");
        }

        boolean result = false;
        String updateType = updateData.getUpdateType();

        try {
            switch (updateType) {
                case "stepComplete":
                    result = handleStepComplete(progress, updateData, operatorId);
                    break;
                case "progressStatus":
                    result = handleProgressStatusUpdate(progress, updateData, operatorId);
                    break;
                case "stepConfig":
                    result = handleStepConfigUpdate(progress, updateData, operatorId);
                    break;
                default:
                    result = handleLegacyUpdate(progress, updateData, operatorId);
                    break;
            }

            if (result) {
                // 记录操作日志
                String logContent = String.format("更新类型: %s", updateType);
                logProgress(progressId, updateType.toUpperCase(), logContent, 
                          updateData.getStepRemark(), operatorId);
            }

            return result;
        } catch (Exception e) {
            throw new RuntimeException("更新进度失败: " + e.getMessage(), e);
        }
    }

    // =========================
    // 步骤配置相关方法实现
    // =========================

    @Override
    public boolean initializeStepsConfig(Long progressId) {
        String stepsConfig = createDefaultStepsConfig();
        return progressMapper.updateStepsConfig(progressId, stepsConfig, null) > 0;
    }

    @Override
    public String getStepsConfig(Long progressId) {
        return progressMapper.selectStepsConfig(progressId);
    }

    @Override
    public boolean updateStepsConfig(Long progressId, String stepsConfig, Long operatorId) {
        return progressMapper.updateStepsConfig(progressId, stepsConfig, operatorId) > 0;
    }

    // =========================
    // 步骤数据相关方法实现
    // =========================

    @Override
    public String getStepsData(Long progressId) {
        return progressMapper.selectStepsData(progressId);
    }

    @Override
    public boolean updateStepsData(Long progressId, String stepsData, Long operatorId) {
        return progressMapper.updateStepsData(progressId, stepsData, operatorId) > 0;
    }

    @Override
    public Map<String, Object> getStepData(Long progressId, String stepCode) {
        String stepsDataJson = getStepsData(progressId);
        if (!StringUtils.hasText(stepsDataJson)) {
            return new HashMap<>();
        }

        try {
            JSONObject stepsData = JSON.parseObject(stepsDataJson);
            JSONObject stepData = stepsData.getJSONObject(stepCode);
            return stepData != null ? stepData : new HashMap<>();
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStepData(Long progressId, String stepCode, Map<String, Object> stepData, Long operatorId) {
        String stepsDataJson = getStepsData(progressId);
        JSONObject stepsData;

        if (StringUtils.hasText(stepsDataJson)) {
            stepsData = JSON.parseObject(stepsDataJson);
        } else {
            stepsData = new JSONObject();
        }

        // 更新指定步骤的数据
        stepsData.put(stepCode, stepData);

        return updateStepsData(progressId, stepsData.toJSONString(), operatorId);
    }

    // =========================
    // 步骤流程控制方法实现
    // =========================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeStep(Long progressId, String stepCode, Map<String, Object> stepData, 
                               UpdateProgressDto.JumpOption jumpOption, Long operatorId) {
        // 更新步骤数据
        updateStepData(progressId, stepCode, stepData, operatorId);

        // 更新步骤配置中的状态
        updateStepStatusInConfig(progressId, stepCode, "COMPLETED", operatorId);

        // 计算下一步骤
        String nextStep = getNextStep(progressId, stepCode, jumpOption);

        if (nextStep != null) {
            // 更新当前步骤
            String nextStepName = STEP_NAMES.get(nextStep);
            progressMapper.updateCurrentStep(progressId, nextStep, nextStepName, operatorId);
        } else {
            // 所有步骤完成，更新整体状态
            progressMapper.updateProgressStatus(progressId, "COMPLETED", operatorId);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean skipStep(Long progressId, String stepCode, String reason, Long operatorId) {
        // 更新步骤配置中的状态为SKIPPED
        updateStepStatusInConfig(progressId, stepCode, "SKIPPED", operatorId);

        // 记录跳过原因
        Map<String, Object> stepData = new HashMap<>();
        stepData.put("remark", reason);
        stepData.put("operationTime", new Date());
        stepData.put("skipped", true);
        updateStepData(progressId, stepCode, stepData, operatorId);

        // 计算下一步骤
        String nextStep = getNextStep(progressId, stepCode, null);

        if (nextStep != null) {
            // 更新当前步骤
            String nextStepName = STEP_NAMES.get(nextStep);
            progressMapper.updateCurrentStep(progressId, nextStep, nextStepName, operatorId);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetToStep(Long progressId, String stepCode, Long operatorId) {
        // 重置到指定步骤，将该步骤之后的所有步骤状态设为PENDING
        String stepsConfigJson = getStepsConfig(progressId);
        if (!StringUtils.hasText(stepsConfigJson)) {
            return false;
        }

        try {
            JSONObject stepsConfig = JSON.parseObject(stepsConfigJson);
            JSONObject steps = stepsConfig.getJSONObject("steps");

            // 找到目标步骤的顺序
            int targetOrder = getStepOrder(stepCode);

            // 重置该步骤及之后的步骤状态
            for (String code : STEP_CODES) {
                int currentOrder = getStepOrder(code);
                if (currentOrder >= targetOrder) {
                    JSONObject step = steps.getJSONObject(code);
                    if (step != null) {
                        step.put("status", "PENDING");
                    }
                }
            }

            // 更新当前步骤
            stepsConfig.put("currentStep", stepCode);

            // 保存配置
            updateStepsConfig(progressId, stepsConfig.toJSONString(), operatorId);

            // 更新主表当前步骤
            String stepName = STEP_NAMES.get(stepCode);
            progressMapper.updateCurrentStep(progressId, stepCode, stepName, operatorId);

            return true;
        } catch (Exception e) {
            throw new RuntimeException("重置步骤失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getNextStep(Long progressId, String currentStepCode, UpdateProgressDto.JumpOption jumpOption) {
        // 如果有跳转选项，按跳转逻辑处理
        if (jumpOption != null) {
            return handleJumpLogic(progressId, currentStepCode, jumpOption);
        }

        // 默认下一步逻辑
        return getDefaultNextStep(currentStepCode);
    }

    @Override
    public boolean canCompleteStep(Long progressId, String stepCode) {
        TStMaterialPlanProgress progress = progressMapper.selectById(progressId);
        if (progress == null || !"IN_PROGRESS".equals(progress.getProgressStatus())) {
            return false;
        }

        // 只有当前步骤可以完成
        return stepCode.equals(progress.getCurrentStep());
    }

    @Override
    public boolean canSkipStep(Long progressId, String stepCode) {
        // 检查步骤是否可跳过
        Set<String> skippableSteps = Set.of(
            "WRITING_PROCESS", "INTERVIEW_INVITATION", "INTERVIEW_TRAINING", 
            "ATTEND_INTERVIEW", "VISA_PREPARATION", "CONDITIONAL_MATERIALS"
        );

        return skippableSteps.contains(stepCode) && canCompleteStep(progressId, stepCode);
    }

    // =========================
    // 进度状态管理方法实现
    // =========================

    @Override
    public boolean updateProgressStatus(Long progressId, String progressStatus, Long operatorId) {
        return progressMapper.updateProgressStatus(progressId, progressStatus, operatorId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeProgress(Long progressId, Long operatorId) {
        return updateProgressStatus(progressId, "COMPLETED", operatorId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelProgress(Long progressId, String reason, Long operatorId) {
        boolean result = updateProgressStatus(progressId, "CANCELLED", operatorId);
        if (result) {
            logProgress(progressId, "CANCEL", "取消申请流程", reason, operatorId);
        }
        return result;
    }

    // =========================
    // 统计和报表方法实现
    // =========================

    @Override
    public Map<String, Object> getStepStatistics(Long progressId) {
        Map<String, Object> statistics = new HashMap<>();
        
        String stepsConfigJson = getStepsConfig(progressId);
        if (!StringUtils.hasText(stepsConfigJson)) {
            return statistics;
        }

        try {
            JSONObject stepsConfig = JSON.parseObject(stepsConfigJson);
            JSONObject steps = stepsConfig.getJSONObject("steps");

            int totalSteps = STEP_CODES.length;
            int completedSteps = 0;
            int skippedSteps = 0;
            int pendingSteps = 0;

            for (String stepCode : STEP_CODES) {
                JSONObject step = steps.getJSONObject(stepCode);
                if (step != null) {
                    String status = step.getString("status");
                    switch (status) {
                        case "COMPLETED":
                            completedSteps++;
                            break;
                        case "SKIPPED":
                            skippedSteps++;
                            break;
                        default:
                            pendingSteps++;
                            break;
                    }
                }
            }

            statistics.put("totalSteps", totalSteps);
            statistics.put("completedSteps", completedSteps);
            statistics.put("skippedSteps", skippedSteps);
            statistics.put("pendingSteps", pendingSteps);

            return statistics;
        } catch (Exception e) {
            return statistics;
        }
    }

    @Override
    public int getProgressPercentage(Long progressId) {
        Map<String, Object> statistics = getStepStatistics(progressId);
        int totalSteps = (Integer) statistics.getOrDefault("totalSteps", 12);
        int completedSteps = (Integer) statistics.getOrDefault("completedSteps", 0);
        int skippedSteps = (Integer) statistics.getOrDefault("skippedSteps", 0);

        return Math.round(((float)(completedSteps + skippedSteps) / totalSteps) * 100);
    }

    @Override
    public String getEstimatedCompletionTime(Long progressId) {
        // 基于历史数据和当前进度计算预计完成时间
        // 这里简化实现，返回基于当前进度的估算
        int percentage = getProgressPercentage(progressId);
        if (percentage >= 100) {
            return "已完成";
        }

        // 简单估算：假设平均每步骤需要7天
        int remainingSteps = (100 - percentage) * 12 / 100;
        int estimatedDays = remainingSteps * 7;

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, estimatedDays);

        return cal.getTime().toString();
    }

    // =========================
    // JSON工具方法实现
    // =========================

    @Override
    public Map<String, Object> parseStepsConfig(String stepsConfig) {
        if (!StringUtils.hasText(stepsConfig)) {
            return new HashMap<>();
        }

        try {
            return JSON.parseObject(stepsConfig, Map.class);
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> parseStepsData(String stepsData) {
        if (!StringUtils.hasText(stepsData)) {
            return new HashMap<>();
        }

        try {
            return JSON.parseObject(stepsData, Map.class);
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    @Override
    public String toStepsConfigJson(Map<String, Object> stepsConfigMap) {
        return JSON.toJSONString(stepsConfigMap);
    }

    @Override
    public String toStepsDataJson(Map<String, Object> stepsDataMap) {
        return JSON.toJSONString(stepsDataMap);
    }

    // =========================
    // 私有辅助方法
    // =========================

    /**
     * 创建默认步骤配置
     */
    private String createDefaultStepsConfig() {
        JSONObject config = new JSONObject();
        config.put("currentStep", "WRITING_PROCESS");
        
        JSONObject steps = new JSONObject();
        for (int i = 0; i < STEP_CODES.length; i++) {
            String stepCode = STEP_CODES[i];
            JSONObject step = new JSONObject();
            step.put("status", "PENDING");
            step.put("order", i + 1);
            step.put("required", isRequiredStep(stepCode));
            step.put("nextStep", i < STEP_CODES.length - 1 ? STEP_CODES[i + 1] : null);
            steps.put(stepCode, step);
        }
        
        config.put("steps", steps);
        config.put("completedSteps", new ArrayList<>());
        config.put("skippedSteps", new ArrayList<>());

        return config.toJSONString();
    }

    /**
     * 创建默认步骤数据
     */
    private String createDefaultStepsData() {
        JSONObject stepsData = new JSONObject();
        
        for (String stepCode : STEP_CODES) {
            JSONObject stepData = new JSONObject();
            stepData.put("remark", "");
            stepsData.put(stepCode, stepData);
        }

        return stepsData.toJSONString();
    }

    /**
     * 处理步骤完成
     */
    private boolean handleStepComplete(TStMaterialPlanProgress progress, UpdateProgressDto updateData, Long operatorId) {
        String stepCode = updateData.getStepCode();
        Map<String, Object> stepData = updateData.getStepData();
        UpdateProgressDto.JumpOption jumpOption = updateData.getJumpOption();

        return completeStep(progress.getId(), stepCode, stepData, jumpOption, operatorId);
    }

    /**
     * 处理进度状态更新
     */
    private boolean handleProgressStatusUpdate(TStMaterialPlanProgress progress, UpdateProgressDto updateData, Long operatorId) {
        return progressMapper.updateProgressStatus(progress.getId(), updateData.getProgressStatus(), operatorId) > 0;
    }

    /**
     * 处理步骤配置更新
     */
    private boolean handleStepConfigUpdate(TStMaterialPlanProgress progress, UpdateProgressDto updateData, Long operatorId) {
        Map<String, Object> stepsConfig = updateData.getStepsConfig();
        String stepsConfigJson = toStepsConfigJson(stepsConfig);
        return updateStepsConfig(progress.getId(), stepsConfigJson, operatorId);
    }

    /**
     * 处理遗留更新方式（向后兼容）
     */
    private boolean handleLegacyUpdate(TStMaterialPlanProgress progress, UpdateProgressDto updateData, Long operatorId) {
        // 处理业务字段更新
        boolean result = false;

        // 更新申请信息
        if (updateData.getApplicationNumber() != null || updateData.getApplicationEmail() != null) {
            result = progressMapper.updateApplicationInfo(progress.getId(),
                updateData.getApplicationNumber(), updateData.getApplicationEmail(),
                updateData.getApplicationPassword(), updateData.getApplicationSubmissionTime(),
                operatorId) > 0;
        }

        // 更新面试信息
        if (updateData.getInterviewLink() != null || updateData.getInterviewDateTime() != null) {
            result = progressMapper.updateInterviewInfo(progress.getId(),
                updateData.getInterviewLink(), updateData.getInterviewDateTime(), operatorId) > 0;
        }

        // 更新录取信息
        if (updateData.getIsReceiveAdmission() != null) {
            result = progressMapper.updateAdmissionInfo(progress.getId(),
                updateData.getIsReceiveAdmission(), updateData.getReceiveAdmissionTime(),
                updateData.getAdmissionType(), updateData.getAdmissionConditions(), operatorId) > 0;
        }

        // 其他业务字段类似处理...

        return result;
    }

    /**
     * 更新步骤配置中的状态
     */
    private void updateStepStatusInConfig(Long progressId, String stepCode, String status, Long operatorId) {
        String stepsConfigJson = getStepsConfig(progressId);
        if (!StringUtils.hasText(stepsConfigJson)) {
            return;
        }

        try {
            JSONObject stepsConfig = JSON.parseObject(stepsConfigJson);
            JSONObject steps = stepsConfig.getJSONObject("steps");
            JSONObject step = steps.getJSONObject(stepCode);
            
            if (step != null) {
                step.put("status", status);
                updateStepsConfig(progressId, stepsConfig.toJSONString(), operatorId);
            }
        } catch (Exception e) {
            // 忽略JSON解析错误
        }
    }

    /**
     * 处理跳转逻辑
     */
    private String handleJumpLogic(Long progressId, String currentStepCode, UpdateProgressDto.JumpOption jumpOption) {
        // 根据跳转选项跳过相应步骤
        if (jumpOption.getSkipSteps() != null && jumpOption.getSkipSteps().length > 0) {
            for (String skipStepCode : jumpOption.getSkipSteps()) {
                updateStepStatusInConfig(progressId, skipStepCode, "SKIPPED", null);
            }
        }

        return jumpOption.getNextStep();
    }

    /**
     * 获取默认下一步
     */
    private String getDefaultNextStep(String currentStepCode) {
        for (int i = 0; i < STEP_CODES.length - 1; i++) {
            if (STEP_CODES[i].equals(currentStepCode)) {
                return STEP_CODES[i + 1];
            }
        }
        return null;
    }

    /**
     * 获取步骤顺序
     */
    private int getStepOrder(String stepCode) {
        for (int i = 0; i < STEP_CODES.length; i++) {
            if (STEP_CODES[i].equals(stepCode)) {
                return i + 1;
            }
        }
        return 0;
    }

    /**
     * 判断是否为必需步骤
     */
    private boolean isRequiredStep(String stepCode) {
        Set<String> requiredSteps = Set.of(
            "WRITING_FINALIZATION", "APPLICATION_FILLING", "ADMISSION_NOTICE",
            "ACCEPT_OFFER_DEPOSIT", "VISA_FORMAL_OFFER", "ACCOMMODATION_REGISTRATION"
        );
        return requiredSteps.contains(stepCode);
    }

    /**
     * 记录操作日志
     */
    private void logProgress(Long progressId, String operationType, String operationContent, String remark, Long operatorId) {
        try {
            TStMaterialPlanProgressLog log = new TStMaterialPlanProgressLog();
            log.setProgressId(progressId);
            log.setOperationType(operationType);
            log.setOperationContent(operationContent);
            log.setOperationRemark(remark);
            log.setOperatorId(operatorId);
            log.setOperationTime(new Date());
            logService.save(log);
        } catch (Exception e) {
            // 日志记录失败不影响主业务
        }
    }
}